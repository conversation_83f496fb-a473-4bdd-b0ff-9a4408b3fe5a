import { useEffect, useState, useCallback, useRef } from "react";
import { type EnrichedActivity, type EnrichedReaction } from "getstream";
import {
  Loader2,
  MessageSquare,
  Heart,
  Share2,
  MoreHorizontal,
  Send,
  ImageIcon,
  X,
} from "lucide-react";
import dayjs from "dayjs";
import relativeTime from "dayjs/plugin/relativeTime";
import utc from "dayjs/plugin/utc";
import timezone from "dayjs/plugin/timezone";

import type { FeedModule as FeedModuleType } from "~/lib/api/types";
import { useAppContext } from "~/lib/providers/app-context";
import { useProfile, useGetUploadUrl } from "~/lib/api/client-queries";
import { canUserPostToFeed } from "~/lib/utils/feed";

// Helper function to check if a file is an image
const isImageFile = (file: File): boolean => {
  return file.type.startsWith("image/");
};

interface FeedModuleProps {
  module: FeedModuleType;
  params: {
    groupId: string;
    cohortId: string;
    moduleId: string;
  };
}

interface EnrichedActivityWithText extends EnrichedActivity {
  text?: string;
  message?: string;
  image?: string;
  attachments?: Array<{
    type: string;
    image_url?: string;
    asset_url?: string;
    custom?: Record<string, any>;
  }>;
  own_reactions?: {
    like?: EnrichedReaction[];
    comment?: EnrichedReaction[];
  };
  reaction_counts?: {
    like?: number;
    comment?: number;
  };
}

type CommentWithUser = EnrichedReaction & {
  user?: {
    id: string;
    created_at: string;
    updated_at: string;
    data: {
      name?: string;
      image?: string;
    };
  };
};

export function FeedModule({ module }: FeedModuleProps) {
  const { userId, streamClient } = useAppContext();
  const { data: profileData } = useProfile();
  const getUploadUrl = useGetUploadUrl();
  const [activities, setActivities] = useState<EnrichedActivityWithText[]>([]);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);
  const [expandedComments, setExpandedComments] = useState<Set<string>>(
    new Set()
  );
  const [commentInputs, setCommentInputs] = useState<Record<string, string>>(
    {}
  );
  const [loadingComments, setLoadingComments] = useState<
    Record<string, boolean>
  >({});
  const [activityComments, setActivityComments] = useState<
    Record<string, CommentWithUser[]>
  >({});
  const [postText, setPostText] = useState("");
  const [postImage, setPostImage] = useState<string | null>(null);
  const [selectedFile, setSelectedFile] = useState<File | null>(null);
  const [isPosting, setIsPosting] = useState(false);
  const [hasNextPage, setHasNextPage] = useState(false);
  const [isLoadingMore, setIsLoadingMore] = useState(false);
  const [nextPageId, setNextPageId] = useState<string | null>(null);
  const scrollContainerRef = useRef<HTMLDivElement>(null);

  // Check if user has permission to post
  const userRole = profileData?.data?.role;
  const userOrgId = profileData?.data?.organizationId;
  const canPost = canUserPostToFeed(userRole, userOrgId, module.config.feedId);

  // Fetch feed when client is ready
  useEffect(() => {
    const initializeFeed = async () => {
      if (!streamClient) return;

      try {
        setLoading(true);
        setError(null);

        // Get the feed using the config from the module
        const feed = streamClient.feed(
          module.config.feedGroup,
          module.config.feedId
        );

        // Fetch activities with enrichment
        const response = await feed.get({
          limit: 25,
          withReactionCounts: true,
          withOwnReactions: true,
          enrich: true, // This is the key parameter for enrichment
        });

        setActivities(response.results as EnrichedActivityWithText[]);

        // Set up pagination
        const hasMore = response.results.length === 25;
        setHasNextPage(hasMore);
        if (hasMore && response.results.length > 0) {
          const lastActivity = response.results[response.results.length - 1];
          setNextPageId(lastActivity.id);
        } else {
          setNextPageId(null);
        }
      } catch (err) {
        console.error("Error initializing feed:", err);
        setError("Failed to load feed. Please try again later.");
      } finally {
        setLoading(false);
      }
    };

    initializeFeed();
  }, [streamClient, module.config]);

  const loadMoreActivities = useCallback(async () => {
    if (!streamClient || !nextPageId || isLoadingMore || !hasNextPage) {
      return;
    }

    setIsLoadingMore(true);
    try {
      const feed = streamClient.feed(
        module.config.feedGroup,
        module.config.feedId
      );

      // Fetch next page using id_lt for pagination
      const response = await feed.get({
        limit: 25,
        id_lt: nextPageId,
        withReactionCounts: true,
        withOwnReactions: true,
        enrich: true,
      });

      // Append new activities to existing ones
      setActivities((prev) => [
        ...prev,
        ...(response.results as EnrichedActivityWithText[]),
      ]);

      // Update pagination state
      const hasMore = response.results.length === 25;
      setHasNextPage(hasMore);
      if (hasMore && response.results.length > 0) {
        const lastActivity = response.results[response.results.length - 1];
        setNextPageId(lastActivity.id);
      } else {
        setNextPageId(null);
      }
    } catch (err) {
      console.error("Error loading more activities:", err);
      setError("Failed to load more activities. Please try again.");
    } finally {
      setIsLoadingMore(false);
    }
  }, [streamClient, nextPageId, isLoadingMore, hasNextPage, module.config]);

  // Infinite scroll effect
  useEffect(() => {
    const scrollContainer = scrollContainerRef.current;
    if (!scrollContainer) return;

    const handleScroll = () => {
      const { scrollTop, scrollHeight, clientHeight } = scrollContainer;
      const distanceFromBottom = scrollHeight - (scrollTop + clientHeight);

      // Trigger load more when user is within 200px of the bottom
      if (distanceFromBottom <= 200 && hasNextPage && !isLoadingMore) {
        loadMoreActivities();
      }
    };

    // Add scroll event listener to the container
    scrollContainer.addEventListener("scroll", handleScroll, { passive: true });

    // Cleanup
    return () => {
      scrollContainer.removeEventListener("scroll", handleScroll);
    };
  }, [loadMoreActivities, hasNextPage, isLoadingMore]);

  const handleLike = async (activityId: string) => {
    if (!streamClient) return;

    try {
      const reaction = await streamClient.reactions.add("like", activityId);

      // Update the activities state with the new reaction
      setActivities((prevActivities) =>
        prevActivities.map((activity) => {
          if (activity.id === activityId) {
            return {
              ...activity,
              own_reactions: {
                ...activity.own_reactions,
                like: [...(activity.own_reactions?.like || []), reaction],
              },
              reaction_counts: {
                ...activity.reaction_counts,
                like: (activity.reaction_counts?.like || 0) + 1,
              },
            };
          }
          return activity;
        })
      );
    } catch (error) {
      console.error("Error adding like:", error);
    }
  };

  const handleUnlike = async (activityId: string, reactionId: string) => {
    if (!streamClient) return;

    try {
      await streamClient.reactions.delete(reactionId);

      // Update the activities state by removing the reaction
      setActivities((prevActivities) =>
        prevActivities.map((activity) => {
          if (activity.id === activityId) {
            return {
              ...activity,
              own_reactions: {
                ...activity.own_reactions,
                like: activity.own_reactions?.like?.filter(
                  (r) => r.id !== reactionId
                ),
              },
              reaction_counts: {
                ...activity.reaction_counts,
                like: Math.max((activity.reaction_counts?.like || 0) - 1, 0),
              },
            };
          }
          return activity;
        })
      );
    } catch (error) {
      console.error("Error removing like:", error);
    }
  };

  const toggleComments = async (activityId: string) => {
    const newExpanded = new Set(expandedComments);

    if (newExpanded.has(activityId)) {
      newExpanded.delete(activityId);
    } else {
      newExpanded.add(activityId);

      // Fetch comments if not already loaded
      if (!activityComments[activityId] && streamClient) {
        setLoadingComments({ ...loadingComments, [activityId]: true });
        try {
          const response = await streamClient.reactions.filter({
            activity_id: activityId,
            kind: "comment",
            limit: 50,
          });

          setActivityComments({
            ...activityComments,
            [activityId]: response.results as CommentWithUser[],
          });
        } catch (error) {
          console.error("Error fetching comments:", error);
        } finally {
          setLoadingComments({ ...loadingComments, [activityId]: false });
        }
      }
    }

    setExpandedComments(newExpanded);
  };

  const handleAddComment = async (activityId: string) => {
    if (!streamClient || !commentInputs[activityId]?.trim()) return;

    try {
      const comment = await streamClient.reactions.add("comment", activityId, {
        text: commentInputs[activityId],
      });

      // Add comment to local state
      const newComment: CommentWithUser = {
        ...comment,
        user: {
          id: userId!,
          created_at: new Date().toISOString(),
          updated_at: new Date().toISOString(),
        },
      };

      setActivityComments({
        ...activityComments,
        [activityId]: [...(activityComments[activityId] || []), newComment],
      });

      // Update activity comment count
      setActivities((prevActivities) =>
        prevActivities.map((activity) => {
          if (activity.id === activityId) {
            return {
              ...activity,
              reaction_counts: {
                ...activity.reaction_counts,
                comment: (activity.reaction_counts?.comment || 0) + 1,
              },
            };
          }
          return activity;
        })
      );

      // Clear input
      setCommentInputs({ ...commentInputs, [activityId]: "" });
    } catch (error) {
      console.error("Error adding comment:", error);
    }
  };

  const handleCreatePost = async () => {
    if (!streamClient || !postText.trim()) return;

    setIsPosting(true);
    try {
      const userFeed = streamClient.feed(
        module.config.feedGroup,
        module.config.feedId
      );

      // Create activity data
      const activityData: any = {
        verb: "post",
        message: postText,
        object: `cohort:${module.config.feedId}`,
        // Add the target feed to specify where this should appear
        to: [`${module.config.feedGroup}:${module.config.feedId}`],
        time: new Date().toISOString(),
      };

      // Handle image upload if there's a selected file
      if (selectedFile) {
        try {
          // Get upload URL from backend
          const uploadUrlResponse = await getUploadUrl.mutateAsync({
            contentType: selectedFile.type,
            fileName: selectedFile.name,
          });

          // Upload file to the presigned URL
          const uploadResponse = await fetch(uploadUrlResponse.uploadUrl, {
            method: "PUT",
            headers: {
              "Content-Type": selectedFile.type,
            },
            body: selectedFile,
          });

          if (!uploadResponse.ok) {
            throw new Error(`Upload failed: ${uploadResponse.statusText}`);
          }

          // Add attachment to activity using the CDN URL
          activityData.attachments = [
            {
              type: isImageFile(selectedFile) ? "image" : "file",
              [isImageFile(selectedFile) ? "image_url" : "asset_url"]:
                uploadUrlResponse.cdnUrl,
              custom: {},
            },
          ];
        } catch (uploadError) {
          console.error("Error uploading file:", uploadError);
          setError("Failed to upload image. Please try again.");
          return;
        }
      }

      // Add the activity to the user's feed
      await userFeed.addActivity(activityData);

      // Re-fetch the feed to get the enriched data including the new post
      const response = await userFeed.get({
        limit: 2,
        withReactionCounts: true,
        withOwnReactions: true,
        enrich: true,
      });

      // Update activities with the fresh enriched data
      setActivities(response.results as EnrichedActivityWithText[]);

      // Reset pagination after adding new post
      const hasMore = response.results.length === 2;
      setHasNextPage(hasMore);
      if (hasMore && response.results.length > 0) {
        const lastActivity = response.results[response.results.length - 1];
        setNextPageId(lastActivity.id);
      } else {
        setNextPageId(null);
      }

      // Clear the form
      setPostText("");
      setPostImage(null);
      setSelectedFile(null);
    } catch (error) {
      console.error("Error creating post:", error);
      setError("Failed to create post. Please try again.");
    } finally {
      setIsPosting(false);
    }
  };

  const handleImageUpload = (e: React.ChangeEvent<HTMLInputElement>) => {
    const file = e.target.files?.[0];
    if (file) {
      // Clear any existing errors
      setError(null);

      // Validate file type and size
      if (!isImageFile(file)) {
        setError("Please select a valid image file.");
        return;
      }

      // Check file size (max 100MB as per GetStream docs)
      const maxSize = 100 * 1024 * 1024; // 100MB in bytes
      if (file.size > maxSize) {
        setError("Image file size must be less than 100MB.");
        return;
      }

      // Store the file for upload and create preview
      setSelectedFile(file);

      // Create a preview using FileReader
      const reader = new FileReader();
      reader.onloadend = () => {
        setPostImage(reader.result as string);
      };
      reader.readAsDataURL(file);
    }

    // Reset the input value so the same file can be selected again if needed
    e.target.value = "";
  };

  // Initialize dayjs with plugins
  dayjs.extend(relativeTime);
  dayjs.extend(utc);
  dayjs.extend(timezone);

  const formatTime = (timestamp: string) => {
    // Parse the UTC timestamp and convert to local timezone
    const date = dayjs.utc(timestamp).local();
    const now = dayjs();
    const diffInDays = now.diff(date, "day");

    // For dates older than 7 days, show formatted date
    if (diffInDays > 7) {
      return date.format("MMM D, YYYY");
    }

    // Use dayjs humanize for recent dates
    return date.fromNow();
  };

  const getActorName = (actor: any): string => {
    if (typeof actor === "string") return actor;
    if (actor?.data?.name) return actor.data.name;
    if (actor?.id) return actor.id;
    return "Unknown User";
  };

  const getActorInitial = (actor: any): string => {
    const name = getActorName(actor);
    return name[0]?.toUpperCase() || "U";
  };

  const getActorImage = (actor: any): string | null => {
    if (actor?.data?.image) return actor.data.image;
    return null;
  };

  const getActivityContent = (activity: EnrichedActivityWithText): string => {
    if (activity.message) return activity.message;
    if (activity.text) return activity.text;
    if (typeof activity.object === "string") return activity.object;
    const obj = activity.object as any;
    if (obj?.text) return obj.text;
    if (obj?.content) return obj.content;
    if (obj?.id) return `${activity.verb} ${obj.id}`;
    return `${activity.verb}`;
  };

  return (
    <div className="flex flex-col h-full">
      {/* Header */}
      <div className="relative w-full h-48 bg-gradient-to-r from-purple-600 to-pink-600">
        <div className="absolute inset-0 bg-black/20" />
        <div className="relative z-10 flex flex-col justify-end h-full p-6">
          <h1 className="text-3xl font-bold text-white">{module.name}</h1>
          <p className="text-white/80 mt-2">
            Stay updated with the latest activities
          </p>
        </div>
      </div>

      {/* Feed Content */}
      <div ref={scrollContainerRef} className="flex-1 overflow-auto bg-gray-50">
        <div className="max-w-2xl mx-auto p-6">
          {/* Create Post Form */}
          {!loading && !error && canPost && (
            <div className="bg-white rounded-lg shadow-sm border border-gray-200 p-6 mb-6">
              <h3 className="text-lg font-semibold text-gray-900 mb-4">
                Create a Post
              </h3>

              <textarea
                value={postText}
                onChange={(e) => setPostText(e.target.value)}
                placeholder="What's on your mind?"
                className="w-full px-4 py-3 text-gray-900 bg-gray-50 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-purple-500 focus:border-transparent resize-none placeholder-gray-400"
                rows={3}
              />

              {postImage && (
                <div className="mt-3 relative">
                  <img
                    src={postImage}
                    alt="Upload preview"
                    className="max-h-64 rounded-lg"
                  />
                  <button
                    onClick={() => {
                      setPostImage(null);
                      setSelectedFile(null);
                    }}
                    className="absolute top-2 right-2 p-1 bg-black/50 rounded-full hover:bg-black/70 transition-colors"
                  >
                    <X className="w-4 h-4 text-white" />
                  </button>
                </div>
              )}

              <div className="mt-4 flex items-center justify-between">
                <div className="flex items-center gap-2">
                  <label className="cursor-pointer">
                    <input
                      type="file"
                      accept="image/*"
                      onChange={handleImageUpload}
                      className="hidden"
                    />
                    <div className="p-2 text-gray-600 hover:bg-gray-100 rounded-lg transition-colors">
                      <ImageIcon className="w-5 h-5" />
                    </div>
                  </label>
                </div>

                <button
                  onClick={handleCreatePost}
                  disabled={!postText.trim() || isPosting}
                  className="px-6 py-2 bg-purple-600 text-white rounded-lg hover:bg-purple-700 disabled:opacity-50 disabled:cursor-not-allowed transition-colors flex items-center gap-2"
                >
                  {isPosting ? (
                    <>
                      <Loader2 className="w-4 h-4 animate-spin" />
                      {selectedFile ? "Uploading & Posting..." : "Posting..."}
                    </>
                  ) : (
                    "Post"
                  )}
                </button>
              </div>
            </div>
          )}

          {loading && (
            <div className="flex items-center justify-center py-12">
              <Loader2 className="w-8 h-8 animate-spin text-purple-600" />
            </div>
          )}

          {error && (
            <div className="bg-red-50 border border-red-200 rounded-lg p-4 text-red-700">
              {error}
            </div>
          )}

          {!loading && !error && activities.length === 0 && (
            <div className="text-center py-12">
              <p className="text-gray-500">
                No activities yet. Check back later!
              </p>
            </div>
          )}

          {!loading && !error && activities.length > 0 && (
            <div className="space-y-4">
              {activities.map((activity) => (
                <div
                  key={activity.id}
                  className="bg-white rounded-lg shadow-sm border border-gray-200 p-6"
                >
                  {/* Activity Header */}
                  <div className="flex items-start justify-between mb-4">
                    <div className="flex items-center gap-3">
                      {getActorImage(activity.actor) ? (
                        <img
                          src={getActorImage(activity.actor)!}
                          alt={getActorName(activity.actor)}
                          className="w-10 h-10 rounded-full object-cover"
                        />
                      ) : (
                        <div className="w-10 h-10 bg-purple-100 rounded-full flex items-center justify-center">
                          <span className="text-purple-600 font-semibold">
                            {getActorInitial(activity.actor)}
                          </span>
                        </div>
                      )}
                      <div>
                        <p className="font-medium text-gray-900">
                          {getActorName(activity.actor)}
                        </p>
                        <p className="text-sm text-gray-500">
                          {formatTime(activity.time)}
                        </p>
                      </div>
                    </div>
                    <button className="text-gray-400 hover:text-gray-600">
                      <MoreHorizontal className="w-5 h-5" />
                    </button>
                  </div>

                  {/* Activity Content */}
                  <div className="mb-4">
                    <p className="text-gray-800">
                      {getActivityContent(activity)}
                    </p>

                    {/* Display legacy image field */}
                    {activity.image && (
                      <img
                        src={activity.image}
                        alt="Activity image"
                        className="mt-3 rounded-lg max-w-full"
                      />
                    )}

                    {/* Display attachments */}
                    {activity.attachments &&
                      activity.attachments.length > 0 && (
                        <div className="mt-3 space-y-2">
                          {activity.attachments.map((attachment, index) => (
                            <div key={index}>
                              {attachment.type === "image" &&
                                attachment.image_url && (
                                  <img
                                    src={attachment.image_url}
                                    alt="Attached image"
                                    className="rounded-lg max-w-full"
                                  />
                                )}
                              {attachment.type === "file" &&
                                attachment.asset_url && (
                                  <a
                                    href={attachment.asset_url}
                                    target="_blank"
                                    rel="noopener noreferrer"
                                    className="inline-flex items-center px-3 py-2 bg-gray-100 rounded-lg hover:bg-gray-200 transition-colors"
                                  >
                                    <span className="text-sm text-gray-700">
                                      📎 View File
                                    </span>
                                  </a>
                                )}
                            </div>
                          ))}
                        </div>
                      )}
                  </div>

                  {/* Activity Actions */}
                  <div className="flex items-center gap-6 pt-4 border-t">
                    {activity.own_reactions?.like?.length ? (
                      <button
                        onClick={() =>
                          handleUnlike(
                            activity.id,
                            activity.own_reactions!.like![0].id
                          )
                        }
                        className="flex items-center gap-2 text-purple-600 hover:text-purple-700 transition-colors"
                      >
                        <Heart className="w-5 h-5 fill-current" />
                        <span className="text-sm">
                          {activity.reaction_counts?.like || 0}
                        </span>
                      </button>
                    ) : (
                      <button
                        onClick={() => handleLike(activity.id)}
                        className="flex items-center gap-2 text-gray-600 hover:text-purple-600 transition-colors"
                      >
                        <Heart className="w-5 h-5" />
                        <span className="text-sm">
                          {activity.reaction_counts?.like || 0}
                        </span>
                      </button>
                    )}
                    <button
                      onClick={() => toggleComments(activity.id)}
                      className="flex items-center gap-2 text-gray-600 hover:text-purple-600 transition-colors"
                    >
                      <MessageSquare className="w-5 h-5" />
                      <span className="text-sm">
                        {activity.reaction_counts?.comment || 0}
                      </span>
                    </button>
                    <button className="flex items-center gap-2 text-gray-600 hover:text-purple-600 transition-colors">
                      <Share2 className="w-5 h-5" />
                      <span className="text-sm">Share</span>
                    </button>
                  </div>

                  {/* Comments Section */}
                  {expandedComments.has(activity.id) && (
                    <div className="mt-4 pt-4 border-t">
                      {/* Comment Input */}
                      <div className="flex gap-2 mb-4">
                        <input
                          type="text"
                          value={commentInputs[activity.id] || ""}
                          onChange={(e) =>
                            setCommentInputs({
                              ...commentInputs,
                              [activity.id]: e.target.value,
                            })
                          }
                          onKeyDown={(e) => {
                            if (e.key === "Enter") {
                              handleAddComment(activity.id);
                            }
                          }}
                          placeholder="Write a comment..."
                          className="flex-1 px-3 py-2 text-gray-900 bg-white border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-purple-500 focus:border-transparent placeholder-gray-400"
                        />
                        <button
                          onClick={() => handleAddComment(activity.id)}
                          disabled={!commentInputs[activity.id]?.trim()}
                          className="px-4 py-2 bg-purple-600 text-white rounded-lg hover:bg-purple-700 disabled:opacity-50 disabled:cursor-not-allowed transition-colors"
                        >
                          <Send className="w-4 h-4" />
                        </button>
                      </div>

                      {/* Comments List */}
                      {loadingComments[activity.id] ? (
                        <div className="flex justify-center py-4">
                          <Loader2 className="w-5 h-5 animate-spin text-purple-600" />
                        </div>
                      ) : (
                        <div className="space-y-3">
                          {activityComments[activity.id]?.map((comment) => (
                            <div key={comment.id} className="flex gap-3">
                              {comment.user?.data?.image ? (
                                <img
                                  src={comment.user.data.image}
                                  alt={comment.user.data.name || "User"}
                                  className="w-8 h-8 rounded-full object-cover"
                                />
                              ) : (
                                <div className="w-8 h-8 bg-gray-200 rounded-full flex items-center justify-center">
                                  <span className="text-gray-600 text-sm font-medium">
                                    {(comment.user?.data?.name ||
                                      "U")[0].toUpperCase()}
                                  </span>
                                </div>
                              )}
                              <div className="flex-1">
                                <div className="bg-gray-100 rounded-lg px-3 py-2">
                                  <p className="text-sm font-medium text-gray-900">
                                    {comment.user?.data?.name || "Anonymous"}
                                  </p>
                                  <p className="text-sm text-gray-700">
                                    {(comment.data as any)?.text ||
                                      comment.data}
                                  </p>
                                </div>
                                <p className="text-xs text-gray-500 mt-1">
                                  {formatTime(comment.created_at)}
                                </p>
                              </div>
                            </div>
                          )) || []}
                          {(!activityComments[activity.id] ||
                            activityComments[activity.id].length === 0) && (
                            <p className="text-sm text-gray-500 text-center py-2">
                              No comments yet. Be the first to comment!
                            </p>
                          )}
                        </div>
                      )}
                    </div>
                  )}
                </div>
              ))}
            </div>
          )}

          {/* Debug: Manual Load More Button */}
          {!loading &&
            !error &&
            activities.length > 0 &&
            hasNextPage &&
            !isLoadingMore && (
              <div className="flex justify-center py-4">
                <button
                  onClick={loadMoreActivities}
                  className="px-4 py-2 bg-blue-600 text-white rounded-lg hover:bg-blue-700"
                >
                  Manual Load More (Debug)
                </button>
              </div>
            )}

          {/* Infinite Scroll Loading Indicator */}
          {!loading && !error && activities.length > 0 && isLoadingMore && (
            <div className="flex justify-center py-8">
              <div className="flex items-center gap-2 text-gray-600">
                <Loader2 className="w-5 h-5 animate-spin" />
                <span>Loading more activities...</span>
              </div>
            </div>
          )}

          {/* End of feed indicator */}
          {!loading &&
            !error &&
            activities.length > 0 &&
            !hasNextPage &&
            !isLoadingMore && (
              <div className="flex justify-center py-8">
                <div className="text-gray-500 text-sm">
                  You've reached the end of the feed
                </div>
              </div>
            )}
        </div>
      </div>
    </div>
  );
}
